# Node modules
node_modules

.cursor/mcp.json
.Untracked

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*

# Environment variables
.env
.dev.vars

# Vite build output
dist

# TypeScript
*.tsbuildinfo

# IDEs and editors
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# MacOS
.DS_Store

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# pnpm store directory
.pnpm-store

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vite cache
.vite

# Coverage directory used by tools like istanbul
coverage

# Temporary files
*.tmp
*.temp

.wrangler

generated/

# Added by <PERSON> Task Master
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Editor directories and files
.idea
.vscode
# OS specific
# Task files
tasks.json
tasks/ 