{"name": "@redwoodjs/starter-standard", "version": "1.0.0", "description": "Build fast, server-driven webapps on Cloudflare with SSR, RSC, and realtime. ", "main": "index.js", "type": "module", "keywords": [], "author": "", "license": "MIT", "private": true, "scripts": {"build": "vite build", "dev": "NODE_ENV=${NODE_ENV:-development} vite dev", "dev:init": "rw-scripts dev-init", "preview": "vite preview", "worker:run": "rw-scripts worker-run", "clean": "npm run clean:vite", "clean:vite": "rm -rf ./node_modules/.vite", "release": "rw-scripts ensure-deploy-env && npm run clean && prisma generate && RWSDK_DEPLOY=1 npm run build && wrangler deploy", "migrate:dev": "prisma generate && wrangler d1 migrations apply DB --local", "migrate:prd": "wrangler d1 migrations apply DB --remote", "migrate:new": "rw-scripts migrate-new", "seed": "npm run worker:run ./src/scripts/seed.ts", "generate": "rw-scripts ensure-env && prisma generate && wrangler types", "check": "npm run generate && npm run types", "types": "tsc", "prepare": "vibe-rules install cursor"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/adapter-d1": "~6.8.2", "@prisma/client": "~6.8.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.2", "@shadcn/ui": "^0.0.4", "@simplewebauthn/browser": "^13.1.0", "@simplewebauthn/server": "^13.1.1", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/vite": "^4.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "resend": "^4.5.1", "rwsdk": "0.0.85", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "zod": "^3.25.7"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "prisma": "~6.8.2", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "vibe-rules": "^0.2.31", "vite": "^6.2.6", "wrangler": "^4.16.0"}, "pnpm": {"onlyBuiltDependencies": ["@prisma/client", "@prisma/engines", "esbuild", "prisma", "sharp", "workerd"]}, "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}